package com.pugwoo.branch.chart.utils;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Slf4j
public class ChartCommonUtils {

    /**
     * 特别约定：值为empty字符串时，返回null
     */
    public static <T> T getConfig(Map<String, Object> configs,
                                  String key,
                                  T defaultValue,
                                  Function<String, T> asString) {
        return getConfig(configs, key, defaultValue, asString, null, null);
    }

    /**
     * 特别约定：值为empty字符串时，返回null
     */
    public static <T> T getConfig(Map<String, Object> configs,
                                  String key,
                                  T defaultValue,
                                  Function<String, T> asString,
                                  Function<Map<String, ?>, T> asMap) {
        return getConfig(configs, key, defaultValue, asString, asMap, null);
    }

    /**
     * 特别约定：值为empty字符串时，返回null
     */
    public static <T> T getConfig(Map<String, Object> configs,
                                  String key,
                                  T defaultValue,
                                  Function<String, T> asString,
                                  Function<Map<String, ?>, T> asMap,
                                  Function<List<?>, T> asList) {
        if (configs == null || key == null) {
            return defaultValue;
        }
        Object value = configs.get(key);
        if (value == null) {
            return defaultValue;
        } else if (value instanceof Map<?,?>) {
            if (asMap == null) {
                log.error("asMap function not provide, configs:{}, key:{}", configs, key);
                return defaultValue;
            } else {
                T t = asMap.apply((Map<String, ?>) value);
                return t == null ? defaultValue : t;
            }
        } else if (value instanceof List<?>) {
            if (asList == null) {
                log.error("asList function not provide, configs:{}, key:{}", configs, key);
                return defaultValue;
            } else {
                T t = asList.apply((List<?>) value);
                return t == null ? defaultValue : t;
            }
        } else {
            String v = value.toString();
            if (v.isEmpty()) {
                return defaultValue;
            }
            if (asString == null) {
                log.error("asString function not provide, configs:{}, key:{}", configs, key);
                return defaultValue;
            } else {
                T t = asString.apply(v);
                return t == null ? defaultValue : t;
            }
        }
    }

    /**
     * 智能按日期时间排序，如果不是日期，则不排序
     */
    public static void smartSortByDate(List<String> datesList) {
        if (datesList == null || datesList.isEmpty()) {
            return;
        }

        // 判断是否都是日期
        boolean isAllDate = true;
        for (String date : datesList) {
            if (StringTools.isBlank(date)) {
                continue;
            }
            if (DateUtils.parseLocalDateTime(date) == null) {
                isAllDate = false;
                break;
            }
        }
        if (!isAllDate) {
            return;
        }

        ListUtils.sortAscNullLast(datesList, o -> {
            LocalDateTime localDateTime = DateUtils.parseLocalDateTime(o);
            return localDateTime == null ? o : DateUtils.format(localDateTime, "yyyy-MM-dd HH:mm:ss.SSS");
        });
    }

    /**index从0开始*/
    public static String makeArray(List<String> list, Map<String, Integer> index) {
        for (int i = 0; i < list.size(); i++) {
            index.put(list.get(i), i);
        }
        return makeArray(list);
    }

    public static String makeArray(List<?> list) {
        StringBuilder sb = new StringBuilder("[");
        for (Object s : list) {
            if (s == null) {
                sb.append("null");
            } else {
                if (s instanceof List) {
                    sb.append(makeArray((List<?>) s));
                } else if (s instanceof Map) {
                    sb.append(makeObject((Map<?, ?>) s));
                } else if (s instanceof String) {
                    sb.append("'").append(s).append("'");
                } else {
                    sb.append(s);
                }
            }
            sb.append(",");
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 将Map转换为JavaScript对象格式
     */
    public static String makeObject(Map<?, ?> map) {
        StringBuilder sb = new StringBuilder("{");
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            Object key = entry.getKey();
            Object value = entry.getValue();

            // 处理key
            if (key instanceof String) {
                sb.append("\"").append(key).append("\"");
            } else {
                sb.append("\"").append(key.toString()).append("\"");
            }

            sb.append(":");

            // 处理value
            if (value == null) {
                sb.append("null");
            } else if (value instanceof List) {
                sb.append(makeArray((List<?>) value));
            } else if (value instanceof Map) {
                sb.append(makeObject((Map<?, ?>) value));
            } else if (value instanceof String) {
                sb.append("\"").append(value).append("\"");
            } else {
                sb.append(value);
            }

            sb.append(",");
        }
        sb.append("}");
        return sb.toString();
    }

    /**当data为null或空时，返回null；此方法会检查data的元素的列数，是2列还是3列还是其它，并保证所有行都是一样的列数*/
    public static Integer determinateColumn(List<List<String>> data) {
        if (ListUtils.isEmpty(data) || data.getFirst() == null) {
            return null;
        }
        int column = data.getFirst().size();
        for (List<String> row : data) {
            if (row.size() != column) {
                throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR,
                        "数据行数不一致，预期是" + column + "列（第一行为准），实际是：" + row.size() + "列，数据: " + JSON.toJson(row));
            }
        }
        return column;
    }

    public static Integer getRank(Map<String, Integer> rankMap, String key) {
        if (rankMap.containsKey(key)) {
            rankMap.put(key, rankMap.get(key) + 1);
        } else {
            rankMap.put(key, 1);
        }
        return rankMap.get(key);
    }

    public static Boolean parseBoolean(Object o) {
        if (o == null) {
            return false;
        }
        if (o instanceof Boolean) {
            return (Boolean) o;
        }
        return "true".equalsIgnoreCase(o.toString().trim()) || "1".equalsIgnoreCase(o.toString().trim());
    }

    public static Map<String, String> toMap(String key, String value) {
        Map<String, String> map = new HashMap<>();
        map.put(key, value);
        return map;
    }

    /**
     * 支持模糊匹配的map获取，会优先用精确匹配
     */
    public static String getWildcardMatch(Map<String, String> map, String wildcardKey) {
        if (map == null || wildcardKey == null) {
            return null;
        }
        String value = map.get(wildcardKey);
        if (value != null) {
            return value;
        }

        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (FilenameUtils.wildcardMatch(wildcardKey, entry.getKey())) {
                return entry.getValue();
            }
        }
        return null;
    }

}
