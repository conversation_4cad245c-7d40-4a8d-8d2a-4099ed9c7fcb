package com.pugwoo.branch.net_check.service;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.net_check.entity.NetCheckResultDO;
import com.pugwoo.dbhelper.model.PageData;

public interface NetCheckResultService {

    /**
     * 通过主键获得数据
     */
    NetCheckResultDO getById(Long id);
    
    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     */
    PageData<NetCheckResultDO> getPage(int page, int pageSize);
    
    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdate(NetCheckResultDO netCheckResultDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

}