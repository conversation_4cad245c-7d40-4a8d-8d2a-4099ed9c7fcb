package com.pugwoo.branch.net_check.web;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.net_check.entity.NetCheckConfigDO;
import com.pugwoo.branch.net_check.service.NetCheckConfigService;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

@RestController
@RequestMapping(value = "/net_check_config")
public class NetCheckConfigController {

    @Autowired
    private NetCheckConfigService netCheckConfigService;
    
    @GetMapping("list")
    public ModelAndView list() {
        return new ModelAndView("net_check/net_check_config");
    }

    @GetMapping("get_page")
    public WebJsonBean<Map<String, Object>> getPage(int page, int pageSize) {
        PageData<NetCheckConfigDO> pageData = netCheckConfigService.getPage(page, pageSize);
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }
    
    @PostMapping("add_or_update")
    public WebJsonBean<Long> addOrUpdate(NetCheckConfigDO netCheckConfigDO) {
        WebCheckUtils.assertNotNull(netCheckConfigDO, "缺少修改的对象参数");

        // TODO check parameters

        ResultBean<Long> result = netCheckConfigService.insertOrUpdate(netCheckConfigDO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @PostMapping("delete")
    public WebJsonBean<Boolean> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(netCheckConfigService.deleteById(id));
    }

    @PostMapping("enable_disable_config")
    public WebJsonBean<?> enableDisableConfig(Long configId, Boolean enabled) {
        WebCheckUtils.assertNotNull(configId, "缺少检查ID");
        WebCheckUtils.assertNotNull(enabled, "缺少开启参数");
        NetCheckConfigDO configDO = new NetCheckConfigDO();
        configDO.setId(configId);
        configDO.setIsEnabled(enabled);
        ResultBean<Long> result = netCheckConfigService.insertOrUpdate(configDO);
        return WebJsonBean.of(result);
    }

}
