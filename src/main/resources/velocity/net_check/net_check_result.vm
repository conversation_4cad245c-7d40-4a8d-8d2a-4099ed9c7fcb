<style>
</style>

<script type="text/x-template" id="netCheckResult">

    <div>
        <el-form :inline="true" @keyup.native.enter="getData">
            <el-form-item label="编号">
                <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
            </el-form-item>
            <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
            <el-form-item>
                <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
                <el-button @click="resetQuery">重置</el-button>
                <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
            </el-form-item>
        </el-form>

        <el-table :data="tableData" border stripe v-loading.body="tableLoading">
            <el-table-column prop="id" label="编号"></el-table-column>
            <el-table-column prop="netCheckConfigId" label="配置编号"></el-table-column>
            <el-table-column prop="netType" label="网络类型"></el-table-column>
            <el-table-column prop="method" label="请求方式"></el-table-column>
            <el-table-column prop="uri" label="请求地址"></el-table-column>
            <el-table-column prop="params" label="参数"></el-table-column>
            <el-table-column prop="times" label="执行次数"></el-table-column>
            <el-table-column prop="assertion" label="断言表达式"></el-table-column>
            <el-table-column prop="costTimeMs" label="耗时(毫秒)"></el-table-column>
            <el-table-column prop="responseContent" label="响应内容"></el-table-column>
            <el-table-column prop="responseBytes" label="响应字节数"></el-table-column>
            <el-table-column prop="responseBps" label="响应速率(字节/秒)"></el-table-column>
            <el-table-column prop="responsePeakBps" label="峰值速率(字节/秒)"></el-table-column>
            <el-table-column prop="isSuccess" label="是否成功"></el-table-column>
            <el-table-column prop="errorMsg" label="错误信息"></el-table-column>
            <el-table-column prop="createTime" label="创建时间"></el-table-column>
            <el-table-column prop="updateTime" label="更新时间"></el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                       :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
        </el-pagination>

        <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false" :append-to-body='true'> <!-- append-to-body修复弹框蒙版问题 -->
            <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
                <el-form-item label="配置编号" prop="netCheckConfigId">
                    <el-input v-model="addEditForm.netCheckConfigId" placeholder="net检查配置表id"></el-input>
                </el-form-item>
                <el-form-item label="网络类型" prop="netType">
                    <el-input v-model="addEditForm.netType" placeholder="网络类型，如HTTP、TCP、PING"></el-input>
                </el-form-item>
                <el-form-item label="请求方式" prop="method">
                    <el-input v-model="addEditForm.method" placeholder="请求方式，例如http的话有GET POST"></el-input>
                </el-form-item>
                <el-form-item label="请求地址" prop="uri">
                    <el-input v-model="addEditForm.uri" placeholder="请求url或唯一连接标识"></el-input>
                </el-form-item>
                <el-form-item label="参数" prop="params">
                    <el-input v-model="addEditForm.params" placeholder="请求体，默认用json格式，实际情况根据net_type来"></el-input>
                </el-form-item>
                <el-form-item label="执行次数" prop="times">
                    <el-input v-model="addEditForm.times" placeholder="本次执行次数"></el-input>
                </el-form-item>
                <el-form-item label="断言表达式" prop="assertion">
                    <el-input v-model="addEditForm.assertion" placeholder="本次检查断言表达式"></el-input>
                </el-form-item>
                <el-form-item label="耗时(毫秒)" prop="costTimeMs">
                    <el-input v-model="addEditForm.costTimeMs" placeholder="本次检查总耗时 毫秒"></el-input>
                </el-form-item>
                <el-form-item label="响应内容" prop="responseContent">
                    <el-input v-model="addEditForm.responseContent" placeholder="本次检查返回值，如循环多次，存其中1次结果，如超过4096，截断"></el-input>
                </el-form-item>
                <el-form-item label="响应字节数" prop="responseBytes">
                    <el-input v-model="addEditForm.responseBytes" placeholder="本次检查返回字节数，总数"></el-input>
                </el-form-item>
                <el-form-item label="响应速率(字节/秒)" prop="responseBps">
                    <el-input v-model="addEditForm.responseBps" placeholder="本次检查返回的速度，byte每秒"></el-input>
                </el-form-item>
                <el-form-item label="峰值速率(字节/秒)" prop="responsePeakBps">
                    <el-input v-model="addEditForm.responsePeakBps" placeholder="本次检查返回速度峰值，byte每秒"></el-input>
                </el-form-item>
                <el-form-item label="是否成功" prop="isSuccess">
                    <el-input v-model="addEditForm.isSuccess" placeholder="执行是否成功 1成功 0失败"></el-input>
                </el-form-item>
                <el-form-item label="错误信息" prop="errorMsg">
                    <el-input v-model="addEditForm.errorMsg" placeholder="错误信息"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
                <el-button @click="showDialog = false">取消</el-button>
                <el-button type="primary" @click="doAddOrEdit">确定</el-button>
            </div>
        </el-dialog>

    </div>

</script>

<script>
    Vue.component('net-check-result', {
        template: '#netCheckResult',
        data: function () {
            const defaultQueryForm = { page: 1, pageSize: 10 }
            const defaultAddForm = {}
            return {
                defaultQueryForm: defaultQueryForm,
                queryForm: Utils.copy(defaultQueryForm),
                addEditForm: Utils.copy(defaultAddForm),
                rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
                total: 0, tableData: [], tableLoading: false,
                showDialog: false, dialogTitle: ''
            }
        },
        props: {
            /*id: Number*/ /*组件的参数在这里定义*/
        },
        created: function() {
            this.getData()
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/net_check_result/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(this.defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/net_check_result/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增检查net结果表' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/net_check_result/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            }
        }
    })
</script>