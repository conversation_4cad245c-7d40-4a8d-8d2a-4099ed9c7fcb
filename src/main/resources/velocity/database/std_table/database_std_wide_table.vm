<!-- database_std_wide_table.vm -->

#set($page_title='标准宽表管理')

#parse("database/std_table/database_std_wide_table_column.vm")
#parse("database/std_table/database_std_wide_table_result.vm")

<style>
    .database-std-wide-table label {width: 120px;color: #99a9bf;}
    .database-std-wide-table .el-form-item {width: 100%;}
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item label="ID">
            <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
        </el-form-item>
        <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
        <el-form-item>
            <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
            <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column type="expand">
            <template slot-scope="props">
                <el-form :data="props" class="database-std-wide-table">
                    <el-form-item label="数据类型">{{props.row.tableDataType}}</el-form-item>
                    <el-form-item label="刷新频率">{{props.row.refreshFreq}}</el-form-item>
                    <el-form-item label="延迟类型">{{props.row.refreshDelayType}}</el-form-item>
                    <el-form-item label="刷新时间">{{props.row.refreshTime}}</el-form-item>
                    <el-form-item label="来源系统">{{props.row.sourceBizSystem}}</el-form-item>
                    <el-form-item label="维护人">{{props.row.maintainer}}</el-form-item>
                    <el-form-item label="关注人">{{props.row.follower}}</el-form-item>
                    <el-form-item label="示例说明">{{props.row.summaryDemo}}</el-form-item>
                    <el-form-item label="文档(MD)">{{props.row.docMarkdown}}</el-form-item>
                    <el-form-item label="文档(HTML)">{{props.row.docHtml}}</el-form-item>
                    <el-form-item label="分区字段">{{props.row.partitionColumn}}</el-form-item>
                    <el-form-item label="分区最小值">{{props.row.partitionMin}}</el-form-item>
                    <el-form-item label="分区最大值">{{props.row.partitionMax}}</el-form-item>
                    <el-form-item label="分区数量">{{props.row.partitionCount}}</el-form-item>
                    <el-form-item label="分区范围">{{props.row.partitionCountRange}}</el-form-item>
                    <el-form-item label="创建时间">{{props.row.createTime}}</el-form-item>
                    <el-form-item label="更新时间">{{props.row.updateTime}}</el-form-item>
                    <el-form-item label="创建人ID">{{props.row.createUserId}}</el-form-item>
                    <el-form-item label="更新人ID">{{props.row.updateUserId}}</el-form-item>
                </el-form>
            </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" width="50"></el-table-column>
        <el-table-column prop="name" label="名称" width="250"></el-table-column>
        <el-table-column label="数据库" width="120">
            <template slot-scope="props">
                <span>{{props.row.database}}</span>
            </template>
        </el-table-column>
        <el-table-column prop="databaseName" label="数据库名" width="150"></el-table-column>
        <el-table-column prop="tableName" label="表名" width="280"></el-table-column>
        <el-table-column prop="summary" label="简介"></el-table-column>
        <el-table-column label="操作" width="200">
            <template slot-scope="scope">
                <el-button-group>
                    <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                    <el-button type="primary" size="small" @click="handleEditColumns(scope.row)">表列</el-button>
                    <el-button size="small" @click="handleDocs(scope.row)">文档</el-button>
                    <el-button size="small" type="warning" @click="handleResult(scope.row)">结果</el-button>
                </el-button-group>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
            <el-form-item label="名称" prop="name">
                <el-input v-model="addEditForm.name" placeholder="标准宽表名称"></el-input>
            </el-form-item>
            <el-form-item label="数据库ID" prop="databaseId">
                <el-select v-model="addEditForm.databaseId" placeholder="请选择数据库">
                    <el-option v-for="item in databases" :key="item.databaseId" :label="item.name" :value="item.databaseId">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="数据库名" prop="databaseName">
                <el-input v-model="addEditForm.databaseName" placeholder="数据库名"></el-input>
            </el-form-item>
            <el-form-item label="表名" prop="tableName">
                <el-input v-model="addEditForm.tableName" placeholder="标准宽表表名"></el-input>
            </el-form-item>
            <el-form-item label="数据类型" prop="tableDataType">
                <el-input v-model="addEditForm.tableDataType" placeholder="表的数据类型，SNAPSHOT全量快照，FULL全量无快照"></el-input>
            </el-form-item>
            <el-form-item label="刷新频率" prop="refreshFreq">
                <el-input v-model="addEditForm.refreshFreq" placeholder="更新频率，用秒来表达，例如按天就是86400"></el-input>
            </el-form-item>
            <el-form-item label="延迟类型" prop="refreshDelayType">
                <el-input v-model="addEditForm.refreshDelayType" placeholder="数据刷新延迟类型，T、T-1、T-2"></el-input>
            </el-form-item>
            <el-form-item label="刷新时间" prop="refreshTime">
                <el-input v-model="addEditForm.refreshTime" placeholder="期望的刷新时间，一般来说，只有按天刷新的才有个期望的刷新时间"></el-input>
            </el-form-item>
            <el-form-item label="来源系统" prop="sourceBizSystem">
                <el-input v-model="addEditForm.sourceBizSystem" placeholder="来源业务系统"></el-input>
            </el-form-item>
            <el-form-item label="维护人" prop="maintainer">
                <el-input v-model="addEditForm.maintainer" placeholder="表的维护人，多个人用逗号隔开"></el-input>
            </el-form-item>
            <el-form-item label="关注人" prop="follower">
                <el-input v-model="addEditForm.follower" placeholder="关注人，多个用逗号隔开"></el-input>
            </el-form-item>
            <el-form-item label="简介" prop="summary">
                <el-input v-model="addEditForm.summary" placeholder="简介，一句话介绍这张表，该表的每一行代表什么"></el-input>
            </el-form-item>
            <el-form-item label="示例说明" prop="summaryDemo">
                <el-input v-model="addEditForm.summaryDemo" placeholder="一个简单的例子来概要说明这张表"></el-input>
            </el-form-item>
            <el-form-item label="分区字段" prop="partitionColumn">
                <el-input v-model="addEditForm.partitionColumn" placeholder="分区字段"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

    <!-- 编辑列对话框，加上v-if可以让对话框销毁，以免内容没刷新 -->
    <el-dialog title="编辑表列" :visible.sync="showColumnDialog" width="90%" top="10px" :close-on-click-modal="false" v-if="showColumnDialog">
        <database-std-wide-table-column :table-id="currentTableId"></database-std-wide-table-column>
        <div slot="footer">
            <el-button @click="showColumnDialog = false">关闭</el-button>
        </div>
    </el-dialog>

    <!-- 文档编辑对话框，这里iframe有特别处理清理，就不用v-if了 -->
    <el-dialog title="编辑文档" :visible.sync="showDocsDialog" width="90%" top="10px" :close-on-click-modal="false">
        <iframe src="/editormd/?sys=branch" style="border: 0; width: 100%; height: 600px" id="editormd" onload="setEditormdContent()"></iframe>
        <div slot="footer">
            <span>
                <el-button @click="cancelAddOrEdit" size="small">取消</el-button>
                <el-button type="primary" @click="doSaveDocs" size="small">确定</el-button>
            </span>
        </div>
    </el-dialog>

    <!-- 执行结果对话框，加上v-if可以让对话框销毁，以免内容没刷新 -->
    <el-dialog title="执行结果" :visible.sync="showResultDialog" width="90%" top="10px" :close-on-click-modal="false" v-if="showResultDialog">
        <database-std-wide-table-result :table-id="currentTableId"></database-std-wide-table-result>
        <div slot="footer">
            <el-button @click="showResultDialog = false">关闭</el-button>
        </div>
    </el-dialog>

</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
            total: 0, tableData: [], tableLoading: false,
            showDialog: false, dialogTitle: '',
            showColumnDialog: false, currentTableId: null,
            showDocsDialog: false, isCloseDialogAfterEditIframeReload: false,
            showResultDialog: false, // 新增的控制结果对话框的变量
            databases: [] // 数据库下拉选择
        },
        created: function() {
            var that = this
            this.getData()
            Resource.get("${_contextPath_}/database/get_database_for_select", {}, function(resp){
                that.databases = resp.data
            })
            window.setEditormdContent = function() {
                var iframe = document.getElementById('editormd');
                if (iframe) {
                    iframe.contentWindow.setEditormdContent(that.addEditForm.docMarkdown);
                }
                if (that.isCloseDialogAfterEditIframeReload) {
                    that.isCloseDialogAfterEditIframeReload = false
                    that.showDocsDialog = false
                }
            }
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/database_std_wide_table/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/database_std_wide_table/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增标准宽表' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/database_std_wide_table/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            },
            handleEditColumns: function(row) {
                this.currentTableId = row.id
                this.showColumnDialog = true
            },
            handleDocs: function(row) { // 只有编辑，没有新增
                this.showDocsDialog = true
                Form.clearError(this, 'addEditForm')
                this.addEditForm = Utils.copy(row)
                this.reloadEditIframe()
            },
            handleResult: function(row) {
                this.currentTableId = row.id
                this.showResultDialog = true
            },
            reloadEditIframe: function() {
                var iframe = document.getElementById('editormd');
                if (iframe) {
                    iframe.contentWindow.location.reload();
                }
            },
            cancelAddOrEdit: function () {
                var that = this
                // 先判断一下内容是否有修改，如果没有修改，那么先隐藏调dialog，再做下面的清理工作，这样页面交互更好
                var iframe = document.getElementById('editormd');
                if (iframe && that.addEditForm.docMarkdown === iframe.contentWindow.getEditormdContent()) {
                    that.showDocsDialog = false
                }

                this.addEditForm = Utils.copy(defaultAddForm)
                this.reloadEditIframe() // 调用这个是为了让浏览器触发提示用户记得保存内容的提示
                this.isCloseDialogAfterEditIframeReload = true
            },
            doSaveDocs: function() {
                var that = this
                var iframe = document.getElementById('editormd');
                if (iframe) {
                    that.addEditForm.docMarkdown = iframe.contentWindow.getEditormdContent()
                    that.addEditForm.docHtml = iframe.contentWindow.getEditormdContentHtml()
                } else {
                    Message.error("无法获取editor的内容")
                    return;
                }
                Resource.post("${_contextPath_}/database_std_wide_table/add_or_update", that.addEditForm, function(resp){
                    iframe.contentWindow.markSaved()
                    Message.success("修改成功")
                    that.showDocsDialog = false
                    that.getData()
                    that.reloadEditIframe()
                })
            },
        }
    })
</script>